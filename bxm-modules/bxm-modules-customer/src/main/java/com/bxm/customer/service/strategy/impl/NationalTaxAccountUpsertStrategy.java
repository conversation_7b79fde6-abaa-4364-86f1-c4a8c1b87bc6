package com.bxm.customer.service.strategy.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.customer.domain.ValueAddedEmployee;
import com.bxm.customer.domain.enums.ValueAddedBizType;
import com.bxm.customer.domain.enums.ValueAddedOperationType;
import com.bxm.customer.mapper.ValueAddedEmployeeMapper;
import com.bxm.customer.service.strategy.AbstractValueAddedEmployeeUpsertStrategy;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * 国税账号业务类型的upsert策略实现
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Slf4j
@Component
public class NationalTaxAccountUpsertStrategy extends AbstractValueAddedEmployeeUpsertStrategy {

    @Autowired
    private ValueAddedEmployeeMapper valueAddedEmployeeMapper;

    @Override
    public Integer getSupportedBizType() {
        return ValueAddedBizType.NATIONAL_TAX_ACCOUNT.getCode();
    }

    @Override
    public void validateBusinessFields(ValueAddedEmployee employee) {
        // 只验证核心业务逻辑，
        // 验证操作类型是否适用于国税账号业务
        if (!ValueAddedOperationType.isValidForBizType(ValueAddedBizType.NATIONAL_TAX_ACCOUNT, employee.getOperationType())) {
            throw new IllegalArgumentException("Invalid operation type for national tax account business: " + employee.getOperationType());
        }

        // 验证税号（国税账号业务必须字段）
        if (StringUtils.isEmpty(employee.getTaxNumber())) {
            throw new IllegalArgumentException("Tax number is required for national tax account business");
        }

        // 验证查询密码（国税账号业务必须字段）
        if (StringUtils.isEmpty(employee.getQueryPassword())) {
            throw new IllegalArgumentException("Query password is required for national tax account business");
        }


    }

    @Override
    public void preprocessEmployee(ValueAddedEmployee employee) {
        // 设置业务类型
        employee.setBizType(ValueAddedBizType.NATIONAL_TAX_ACCOUNT.getCode());

        // 清理其他业务类型专用字段
        employee.setSocialInsurancePackage(null);
        employee.setGrossSalary(null);
        employee.setProvidentFundPersonal(null);

        // 标准化税号（去除空格，转大写）
        if (StringUtils.isNotEmpty(employee.getTaxNumber())) {
            employee.setTaxNumber(employee.getTaxNumber().trim().toUpperCase());
        }

        // 查询密码加密处理（这里简单示例，实际应该使用加密算法）
        if (StringUtils.isNotEmpty(employee.getQueryPassword())) {
            // 注意：实际项目中应该使用适当的加密方法
            // employee.setQueryPassword(encryptPassword(employee.getQueryPassword()));
        }


    }

    @Override
    public void postprocessEmployee(ValueAddedEmployee employee, boolean isUpdate) {
        String operation = isUpdate ? "updated" : "created";
        log.info("National tax account employee {} successfully: ID={}, Name={}, Operation={}, TaxNumber={}", operation, employee.getId(), employee.getEmployeeName(), employee.getOperationType(), employee.getTaxNumber());

        // 国税账号业务的后处理逻辑
        // 例如：同步到国税系统、发送通知等
    }

    @Override
    public ValueAddedEmployee findExistingEmployee(ValueAddedEmployee employee) {
        // 国税账号业务的唯一性判断：交付单编号 + 身份证号 + 业务类型
        // 也可以考虑税号的唯一性
        LambdaQueryWrapper<ValueAddedEmployee> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ValueAddedEmployee::getDeliveryOrderNo, employee.getDeliveryOrderNo())
                .eq(ValueAddedEmployee::getIdNumber, employee.getIdNumber())
                .eq(ValueAddedEmployee::getBizType, ValueAddedBizType.NATIONAL_TAX_ACCOUNT.getCode());

        return valueAddedEmployeeMapper.selectOne(queryWrapper);
    }

    @Override
    public ValueAddedEmployee mergeEmployee(ValueAddedEmployee existing, ValueAddedEmployee newEmployee) {
        // 更新基础信息
        existing.setEmployeeName(newEmployee.getEmployeeName());
        existing.setMobile(newEmployee.getMobile());
        existing.setOperationType(newEmployee.getOperationType());
        existing.setEntryType(newEmployee.getEntryType());

        // 更新国税账号特定字段
        if (StringUtils.isNotEmpty(newEmployee.getTaxNumber())) {
            existing.setTaxNumber(newEmployee.getTaxNumber());
        }
        if (StringUtils.isNotEmpty(newEmployee.getQueryPassword())) {
            existing.setQueryPassword(newEmployee.getQueryPassword());
        }
        if (StringUtils.isNotEmpty(newEmployee.getExtendInfo())) {
            existing.setExtendInfo(newEmployee.getExtendInfo());
        }
        if (StringUtils.isNotEmpty(newEmployee.getRemark())) {
            existing.setRemark(newEmployee.getRemark());
        }
        if (newEmployee.getStatus() != null) {
            existing.setStatus(newEmployee.getStatus());
        }

        return existing;
    }


}
